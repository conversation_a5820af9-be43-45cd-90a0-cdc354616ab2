# 简化的SCM数据生成和DAG提取工具

## 概述

`data_generator_simple.py` 是基于 `scm_test.py` 学习的简化数据生成工具，专门用于：

1. **生成原始训练集和测试集**
2. **生成干预/扰动数据集**（可选）
3. **提取和保存DAG图**
4. **提供数据集的基本信息和摘要**

## 主要功能

### SimpleDataGenerator 类

核心数据生成器类，提供以下主要方法：

- `generate_scm_data()`: 生成SCM数据集
- `extract_dag_info()`: 提取DAG图信息
- `save_dag_visualization()`: 保存DAG可视化图
- `get_dataset_summary()`: 获取数据集摘要

## 使用方法

### 基本使用

```python
from data_generator_simple import SimpleDataGenerator

# 初始化生成器
generator = SimpleDataGenerator(device='cpu', seed=42)

# 生成带干预的数据集
datasets = generator.generate_scm_data(
    n_datasets=2,
    dag_type='random_SF',
    dag_size='small',
    generate_intervention=True
)

# 处理每个数据集
for dataset in datasets:
    print(f"数据集: {dataset['dataset_name']}")
    print(f"训练集大小: {dataset['train_x'].shape}")
    print(f"测试集大小: {dataset['test_x_original'].shape}")
    print(f"干预测试集大小: {dataset['test_x_intervention'].shape}")
    
    # 保存DAG图
    generator.save_dag_visualization(dataset['scm'], f"dag_{dataset['dataset_name']}.png")
```

### 参数说明

#### generate_scm_data() 参数

- `n_datasets`: 生成数据集数量（默认1）
- `dag_type`: DAG类型
  - `'random_SF'`: 随机无标度网络
  - `'random_ER'`: 随机ER图
  - `'common_cause'`: 共同原因结构
  - `'chain'`: 链式结构
  - `'fork'`: 分叉结构
  - `'collider'`: 碰撞结构
- `dag_size`: DAG规模（'small', 'medium', 'large'）
- `generate_intervention`: 是否生成干预数据（默认True）
- `intervention_node_type`: 干预节点类型（默认'all_non_family'）
- `custom_functions`: 自定义函数配置

### 数据集结构

每个生成的数据集包含以下字段：

```python
dataset = {
    'dataset_name': str,           # 数据集名称
    'train_x': np.ndarray,         # 训练特征
    'train_y': np.ndarray,         # 训练目标
    'test_x_original': np.ndarray, # 原始测试特征（如有干预）
    'test_y_original': np.ndarray, # 原始测试目标（如有干预）
    'test_x_intervention': np.ndarray, # 干预测试特征（如有干预）
    'test_y_intervention': np.ndarray, # 干预测试目标（如有干预）
    'test_x': np.ndarray,          # 测试特征（无干预时）
    'test_y': np.ndarray,          # 测试目标（无干预时）
    'scm': SCM,                    # SCM模型实例
    'has_intervention': bool,      # 是否包含干预数据
    'dag_info': dict,              # DAG信息
    'feature_names': list,         # 特征名称列表
    'target_name': str             # 目标变量名称
}
```

### DAG信息结构

```python
dag_info = {
    'nodes': list,                 # 节点列表
    'edges': list,                 # 边列表
    'num_nodes': int,              # 节点数量
    'num_edges': int,              # 边数量
    'selected_target': str,        # 选中的目标节点
    'selected_features': list,     # 选中的特征节点
    'intervention_nodes': list,    # 干预节点
    'node_relationships': dict,    # 节点关系（父子、兄弟等）
    'snr_validation': dict         # SNR验证结果（如有）
}
```

## 运行示例

直接运行文件查看完整示例：

```bash
python data_generator_simple.py
```

这将：
1. 生成2个带干预的数据集
2. 生成1个标准数据集
3. 保存所有DAG可视化图
4. 保存所有数据到CSV文件
5. 生成数据集摘要报告

## 输出文件

运行后会在 `simple_data_output_YYYYMMDD_HHMMSS/` 目录下生成：

- `dag_intervention_*.png`: 干预数据集的DAG图
- `dag_standard_*.png`: 标准数据集的DAG图
- `train_data_*.csv`: 训练数据
- `test_original_*.csv`: 原始测试数据
- `test_intervention_*.csv`: 干预测试数据
- `standard_train_*.csv`: 标准训练数据
- `standard_test_*.csv`: 标准测试数据
- `summary.json`: 数据集摘要信息

## 自定义函数配置

可以通过 `custom_functions` 参数自定义节点函数：

```python
custom_functions = {
    'target': {
        'type': 'linear',
        'target_snr': 2.0
    },
    'target_child': {
        'type': 'polynomial',
        'degree': 2,
        'target_snr': 1.5
    },
    'Other_type': {
        'type': 'random_neural_network',
        'hidden_dim': 3,
        'depth': 2,
        'target_snr': 1.0
    }
}
```

## 依赖项

确保已安装以下依赖：
- torch
- numpy
- pandas
- matplotlib
- networkx
- 原项目的相关模块（scm_data_generator, utils_scm, scm_model）

## 注意事项

1. 确保原项目的所有依赖都已正确安装
2. 如果使用GPU，确保CUDA环境配置正确
3. 生成大量数据集时注意内存使用
4. DAG可视化需要graphviz支持
