"""
data_generator_simple.py - 简化的数据生成和DAG提取工具

基于scm_test.py的数据生成方法，提供简化的接口用于：
1. 生成原始训练集和测试集
2. 生成干预/扰动数据集（如果需要）
3. 提取DAG图并保存
4. 提供数据集的基本信息

主要功能：
- generate_scm_data(): 生成SCM数据集
- extract_dag_info(): 提取DAG图信息
- save_dag_visualization(): 保存DAG可视化图
- split_train_test(): 分割训练测试数据
"""

import os
import numpy as np
import torch
import matplotlib.pyplot as plt
import networkx as nx
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

# 导入必要的模块
from scm_data_generator import generate_datasets
from utils_scm import draw_graph
from scm_model import SCM


@dataclass
class FunctionConfig:
    """
    单个函数配置类

    支持的函数类型:
    - 'linear': 线性函数 y = a1*x1 + a2*x2 + ... + b
    - 'polynomial': 多项式函数
    - 'polynomial_even': 偶次多项式函数
    - 'exponential': 指数函数
    - 'logarithmic': 对数函数
    - 'sigmoid_scaled': 缩放的Sigmoid函数
    - 'tanh_scaled': 缩放的Tanh函数
    - 'sine': 正弦函数
    - 'cosine': 余弦函数
    - 'relu_quadratic': ReLU二次函数
    - 'gaussian_rbf': 高斯径向基函数
    - 'gaussian_process': 高斯过程函数（需要GPy）
    - 'fourier_series': 傅里叶级数函数
    - 'piecewise_linear': 分段线性函数
    - 'random_neural_network': 随机神经网络函数

    噪声配置优先级:
    1. target_snr: 基于信噪比动态计算噪声标准差
    2. noise_std: 固定噪声标准差
    3. 如果都未指定，使用全局SNR配置
    """
    function_type: str = 'linear'  # 默认使用线性函数，最简单且稳定
    hidden_dim: int = 2  # 神经网络隐藏层维度（仅对neural_network类型有效）
    depth: int = 3  # 神经网络深度（仅对neural_network类型有效）
    activation: str = 'tanh'  # 激活函数（仅对neural_network类型有效）
    target_snr: Optional[float] = None  # 目标信噪比
    noise_std: Optional[float] = None  # 固定噪声标准差（与target_snr互斥）
    noise_mean: Optional[float] = None  # 噪声均值配置
    noise_mean_mode: str = 'fixed'  # 噪声均值模式：'fixed' 或 'signal_mean'

    # 函数特定参数（根据function_type使用）
    coefficients: Optional[List[float]] = None  # 线性函数系数（linear类型）
    bias: Optional[float] = None  # 偏置项
    degree: Optional[int] = None  # 多项式次数（polynomial类型）
    scale: Optional[float] = None  # 缩放因子
    frequency: Optional[float] = None  # 频率参数（sine/cosine类型）
    n_components: Optional[int] = None  # 组件数量（fourier_series/piecewise_linear类型）
    centers: Optional[List[List[float]]] = None  # 中心点（gaussian_rbf类型）

    # g函数配置（后非线性变换）
    g_function_type: str = 'identity'  # 简单g函数类型，支持: identity, sigmoid, tanh, softplus, elu+1, scaled_tanh
    g_function_config: Optional[Dict] = None  # 完整g函数配置，支持所有function_generator.py中的函数类型


@dataclass
class NodeFunctionConfigs:
    """节点函数配置类 - 支持不同节点类型使用不同函数"""
    target_config: FunctionConfig = None  # target节点（parent）的函数配置
    target_child_config: FunctionConfig = None  # target_child节点的函数配置
    other_config: FunctionConfig = None  # Other_type节点的函数配置

    def __post_init__(self):
        # 如果没有指定，使用默认配置
        if self.target_config is None:
            self.target_config = FunctionConfig()
        if self.target_child_config is None:
            self.target_child_config = FunctionConfig()
        if self.other_config is None:
            self.other_config = FunctionConfig()


@dataclass
class SNRConfig:
    """SNR配置类"""
    parent_snr_values: List[float] = None
    child_snr_multipliers: List[float] = None
    other_snr_multipliers: List[float] = None

    def __post_init__(self):
        if self.parent_snr_values is None:
            self.parent_snr_values = [2.0]
        if self.child_snr_multipliers is None:
            self.child_snr_multipliers = [1.0]
        if self.other_snr_multipliers is None:
            self.other_snr_multipliers = [1.0]


@dataclass
class TestConfig:
    """测试配置类"""
    n_datasets: int = 2
    intervention_node_type: str = 'all_non_family'
    intervention_value_method: str = 'sample'
    custom_dag_type: str = 'random_SF'
    custom_dag_size: str = 'small'
    avg_in_degree: Tuple[float, float] = (2.2, 2.7)
    device: str = 'cuda'
    num_workers: int = 8
    r2_threshold: float = 0.5
    save_json_file: bool = True
    calculate_correlation_mi: bool = True
    extract_function_weights: bool = True
    annotate_all_datasets: bool = True
    annotate_effective_datasets: bool = True

    # SNR和函数配置
    snr_config: SNRConfig = None
    node_function_configs: NodeFunctionConfigs = None

    def __post_init__(self):
        if self.snr_config is None:
            self.snr_config = SNRConfig()
        if self.node_function_configs is None:
            self.node_function_configs = NodeFunctionConfigs()


class DataProcessor:
    """数据处理工具类"""

    @staticmethod
    def convert_tensors_to_numpy(*tensors):
        """将tensor转换为numpy数组"""
        return [tensor.clone().cpu().numpy() if torch.is_tensor(tensor) else tensor for tensor in tensors]

    @staticmethod
    def split_train_test(x, y, split_ratio=0.7):
        """分割训练测试数据"""
        eval_position = int(x.shape[0] * split_ratio)
        return (x[:eval_position], y[:eval_position],
                x[eval_position:], y[eval_position:])

    @staticmethod
    def apply_scaling(scaler, *arrays):
        """应用数据缩放"""
        if scaler is None:
            return arrays
        return [scaler.transform(arr) for arr in arrays]


class SimpleDataGenerator:
    """简化的数据生成器类"""

    def __init__(self, device='cpu', seed=42, test_config: Optional[TestConfig] = None):
        """
        初始化数据生成器

        参数:
            device: 计算设备 ('cpu' 或 'cuda')
            seed: 随机种子
            test_config: 测试配置，如果不提供则使用默认配置
        """
        self.device = device
        self.seed = seed
        self.test_config = test_config if test_config is not None else TestConfig()
        self.h_config = self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'device': self.device,
            'min_noise_std': 0.01,
            'max_noise_std': 0.1,
            'min_init_std': 1,
            'max_init_std': 5,
            'root_distribution': 'gaussian',
            'root_mean': 0.0,
            'root_std': 1.0,
            'sample_root_std': False,
            'min_root': 0.0,
            'max_root': 1.0,
            'max_range': 0.5,
            'sample_cause_ranges': False,
            'sample_std': False,
            'min_num_samples': 1000,
            'max_num_samples': 1000,
            'train_test_split_ratio': 0.7,
            'task': 'regression',
            'min_output_multiclass_ordered_p': 0.0,
            'max_output_multiclass_ordered_p': 0.5,
            'categorical_feature_p': 0,
            'min_drop_node_ratio': 0,
            'max_drop_node_ratio': 0,
            'min_num_node': 5,
            'max_num_node': 20,
            'num_layers': 3,
            'max_num_children': 10,
            'max_num_classes': 5,
            'single_effect_last_layer': False,
            'last_layer_fix_num_node': False,
            'num_node_last_layer': 5,
            'use_monte_carlo_precompute': False
        }
    
    def generate_scm_data(self, 
                         n_datasets: int = 1,
                         dag_type: str = 'random_SF',
                         dag_size: str = 'small',
                         generate_intervention: bool = True,
                         intervention_node_type: str = 'all_non_family',
                         intervention_value_method: str = 'sample',
                         custom_functions: Optional[Dict] = None,
                         avg_in_degree: Tuple[float, float] = (1.2, 1.7)) -> List[Dict]:
        """
        生成SCM数据集
        
        参数:
            n_datasets: 生成数据集数量
            dag_type: DAG类型 ('random_SF', 'random_ER', 'common_cause', 'chain', 'fork', 'collider')
            dag_size: DAG规模 ('small', 'medium', 'large')
            generate_intervention: 是否生成干预数据
            intervention_node_type: 干预节点类型
            intervention_value_method: 干预值生成方法
            custom_functions: 自定义函数配置
            avg_in_degree: 平均入度范围
            
        返回:
            数据集列表，每个元素包含训练集、测试集、干预集（如果有）和DAG信息
        """
        print(f"开始生成 {n_datasets} 个SCM数据集...")
        print(f"配置: DAG类型={dag_type}, 规模={dag_size}, 生成干预数据={generate_intervention}")
        
        # 生成数据集
        if generate_intervention:
            # 生成带干预的数据集
            datasets = generate_datasets(
                num_dataset=n_datasets,
                h_config=self.h_config,
                perturbation_type='counterfactual',  # 使用反事实扰动
                perturbation_node_type=intervention_node_type,
                perturbation_value_method=intervention_value_method,
                custom_dag_type=dag_type,
                custom_functions=custom_functions,
                custom_dag_size=dag_size,
                node_unobserved=False,
                seed=self.seed,
                allow_skip=True,
                avg_in_degree=avg_in_degree
            )
        else:
            # 生成标准数据集
            datasets = generate_datasets(
                num_dataset=n_datasets,
                h_config=self.h_config,
                custom_dag_type=dag_type,
                custom_functions=custom_functions,
                custom_dag_size=dag_size,
                node_unobserved=False,
                seed=self.seed,
                allow_skip=True,
                avg_in_degree=avg_in_degree
            )
        
        # 处理生成的数据集
        processed_datasets = []
        for i, dataset in enumerate(datasets):
            processed_data = self._process_single_dataset(dataset, i, generate_intervention)
            processed_datasets.append(processed_data)
            
        print(f"成功生成 {len(processed_datasets)} 个数据集")
        return processed_datasets
    
    def _process_single_dataset(self, dataset: List, dataset_idx: int, has_intervention: bool) -> Dict:
        """处理单个数据集"""
        dataset_name = dataset[0]
        scm = dataset[-2] if len(dataset) > 5 else dataset[-1]
        
        print(f"处理数据集 {dataset_idx + 1}: {dataset_name}")
        
        if has_intervention:
            # 带干预的数据集格式: [name, x_original, y_original, x_intervention, y_intervention, scm, data_info]
            x_original, y_original = DataProcessor.convert_tensors_to_numpy(dataset[1], dataset[2])
            x_intervention, y_intervention = DataProcessor.convert_tensors_to_numpy(dataset[3], dataset[4])

            # 分割训练测试数据
            train_test_ratio = self.h_config.get('train_test_split_ratio', 0.7)
            train_x, train_y, test_x_original, test_y_original = DataProcessor.split_train_test(
                x_original, y_original, train_test_ratio
            )
            _, _, test_x_intervention, test_y_intervention = DataProcessor.split_train_test(
                x_intervention, y_intervention, train_test_ratio
            )
            
            result = {
                'dataset_name': dataset_name,
                'train_x': train_x,
                'train_y': train_y,
                'test_x_original': test_x_original,
                'test_y_original': test_y_original,
                'test_x_intervention': test_x_intervention,
                'test_y_intervention': test_y_intervention,
                'scm': scm,
                'has_intervention': True
            }
        else:
            # 标准数据集格式: [name, x, y, scm, data_info]
            x, y = DataProcessor.convert_tensors_to_numpy(dataset[1], dataset[2])

            # 分割训练测试数据
            train_test_ratio = self.h_config.get('train_test_split_ratio', 0.7)
            train_x, train_y, test_x, test_y = DataProcessor.split_train_test(x, y, train_test_ratio)
            
            result = {
                'dataset_name': dataset_name,
                'train_x': train_x,
                'train_y': train_y,
                'test_x': test_x,
                'test_y': test_y,
                'scm': scm,
                'has_intervention': False
            }
        
        # 添加DAG信息
        dag_info = self.extract_dag_info(scm)
        result['dag_info'] = dag_info
        
        # 添加特征信息
        feature_names = scm.selected_features if hasattr(scm, 'selected_features') else [f'X{i}' for i in range(train_x.shape[1])]
        result['feature_names'] = feature_names
        result['target_name'] = getattr(scm, 'selected_target', 'Y')
        
        return result

    def extract_dag_info(self, scm: SCM) -> Dict:
        """
        提取DAG图信息

        参数:
            scm: SCM模型实例

        返回:
            包含DAG信息的字典
        """
        dag_info = {
            'nodes': list(scm.dag.nodes()),
            'edges': list(scm.dag.edges()),
            'num_nodes': len(scm.dag.nodes()),
            'num_edges': len(scm.dag.edges()),
            'selected_target': getattr(scm, 'selected_target', None),
            'selected_features': getattr(scm, 'selected_features', []),
            'intervention_nodes': getattr(scm, 'intervention_nodes', None),
            'perturbation_nodes': getattr(scm, 'perturbation_nodes', None),
            'unobserved_nodes': getattr(scm, 'unobserved_nodes', None),
        }

        # 添加SNR信息（如果可用）
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
            dag_info['snr_validation'] = scm.snr_validation_results

        # 添加节点关系信息
        if dag_info['selected_target'] is not None:
            from utils_scm import get_exclusive_node_relationships
            relationships = get_exclusive_node_relationships(scm.dag, dag_info['selected_target'])
            dag_info['node_relationships'] = {
                'parents': list(relationships['parents']),
                'children': list(relationships['children']),
                'spouses': list(relationships['spouses']),
                'grandparents': list(relationships['grandparents']),
                'siblings': list(relationships['siblings']),
                'others': list(relationships['others'])
            }

        return dag_info

    def save_dag_visualization(self, scm: SCM, save_path: str,
                              title: Optional[str] = None) -> str:
        """
        保存DAG可视化图

        参数:
            scm: SCM模型实例
            save_path: 保存路径
            title: 图标题

        返回:
            保存的文件路径
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 获取干预/扰动节点
        intervention_or_perturb_nodes = getattr(scm, 'intervention_nodes', None)
        if intervention_or_perturb_nodes is None:
            intervention_or_perturb_nodes = getattr(scm, 'perturbation_nodes', None)

        # 绘制并保存DAG图
        draw_graph(
            scm.dag,
            save_path,
            target_node=getattr(scm, 'selected_target', None),
            intervention_nodes=intervention_or_perturb_nodes,
            unobserved_nodes=getattr(scm, 'unobserved_nodes', None),
            selected_features=getattr(scm, 'selected_features', None),
            assignment=getattr(scm, 'assignment', None),
            scm=scm,
            model_results=None
        )

        print(f"DAG图已保存到: {save_path}")
        return save_path

    def get_dataset_summary(self, processed_datasets: List[Dict]) -> Dict:
        """
        获取数据集摘要信息

        参数:
            processed_datasets: 处理后的数据集列表

        返回:
            摘要信息字典
        """
        if not processed_datasets:
            return {}

        summary = {
            'total_datasets': len(processed_datasets),
            'datasets_with_intervention': sum(1 for d in processed_datasets if d['has_intervention']),
            'datasets_without_intervention': sum(1 for d in processed_datasets if not d['has_intervention']),
        }

        # 统计数据集规模
        train_sizes = [d['train_x'].shape[0] for d in processed_datasets]
        test_sizes = [d.get('test_x_original', d.get('test_x', np.array([]))).shape[0] for d in processed_datasets]
        feature_counts = [d['train_x'].shape[1] for d in processed_datasets]

        summary.update({
            'avg_train_size': np.mean(train_sizes),
            'avg_test_size': np.mean(test_sizes),
            'avg_feature_count': np.mean(feature_counts),
            'min_train_size': np.min(train_sizes),
            'max_train_size': np.max(train_sizes),
            'min_feature_count': np.min(feature_counts),
            'max_feature_count': np.max(feature_counts),
        })

        # 统计DAG信息
        dag_node_counts = [d['dag_info']['num_nodes'] for d in processed_datasets]
        dag_edge_counts = [d['dag_info']['num_edges'] for d in processed_datasets]

        summary.update({
            'avg_dag_nodes': np.mean(dag_node_counts),
            'avg_dag_edges': np.mean(dag_edge_counts),
            'min_dag_nodes': np.min(dag_node_counts),
            'max_dag_nodes': np.max(dag_node_counts),
        })

        return summary

    def generate_custom_functions_configs(self, snr_config: SNRConfig,
                                        node_function_configs: NodeFunctionConfigs) -> Dict[str, Dict]:
        """生成自定义函数配置"""
        custom_functions_configs = {}

        # 使用配置类生成SNR配置
        for i in snr_config.parent_snr_values:
            for j_mult in snr_config.child_snr_multipliers:
                for k_mult in snr_config.other_snr_multipliers:
                    j = i * j_mult
                    k = max(i, j) * k_mult

                    key = f'parent_snr_{i}_child_snr_{j}_other_snr_{k}'
                    parent_snr = float(i)
                    child_snr = float(j)
                    other_snr = float(k)

                    # 为每个节点类型生成不同的函数配置
                    def create_node_config(node_config, snr_value):
                        """创建单个节点的配置字典"""
                        config = {
                            'type': node_config.function_type
                        }

                        # 神经网络相关参数（仅对neural_network类型有效）
                        if node_config.function_type == 'random_neural_network':
                            config['hidden_dim'] = node_config.hidden_dim
                            config['depth'] = node_config.depth
                            config['activation'] = node_config.activation

                        # 函数特定参数
                        if node_config.coefficients is not None:
                            config['coefficients'] = node_config.coefficients
                        if node_config.bias is not None:
                            config['bias'] = node_config.bias
                        if node_config.degree is not None:
                            config['degree'] = node_config.degree
                        if node_config.scale is not None:
                            config['scale'] = node_config.scale
                        if node_config.frequency is not None:
                            config['frequency'] = node_config.frequency
                        if node_config.n_components is not None:
                            config['n_components'] = node_config.n_components
                        if node_config.centers is not None:
                            config['centers'] = node_config.centers

                        # SNR和noise_std互斥配置
                        if node_config.target_snr is not None:
                            # 如果节点配置中指定了target_snr，使用节点配置的值
                            config['target_snr'] = node_config.target_snr
                        elif node_config.noise_std is not None:
                            # 如果节点配置中指定了noise_std，使用固定噪声标准差
                            config['noise_std'] = node_config.noise_std
                        else:
                            # 否则使用SNR配置生成的值
                            config['target_snr'] = snr_value

                        # 噪声均值配置
                        if node_config.noise_mean is not None:
                            config['noise_mean'] = node_config.noise_mean

                        # 噪声均值模式配置
                        if node_config.noise_mean_mode != 'fixed':
                            config['noise_mean_mode'] = node_config.noise_mean_mode

                        # g函数配置
                        if node_config.g_function_config is not None:
                            # 如果有完整的g函数配置，使用完整配置
                            config['g_function_config'] = node_config.g_function_config
                        elif node_config.g_function_type != 'identity':
                            # 否则使用简单的g函数类型
                            config['g_function_type'] = node_config.g_function_type

                        return config

                    target_function_config = create_node_config(node_function_configs.target_config, parent_snr)
                    target_child_function_config = create_node_config(node_function_configs.target_child_config, child_snr)
                    other_function_config = create_node_config(node_function_configs.other_config, other_snr)

                    custom_functions_configs[key] = {
                        'name': key,
                        'config': {
                            'target': target_function_config,
                            'target_child': target_child_function_config,
                            'Other_type': other_function_config
                        }
                    }

        return custom_functions_configs


def create_custom_functions_example():
    """创建自定义函数配置示例"""
    return {
        'target': {
            'type': 'linear',
            'target_snr': 2.0
        },
        'target_child': {
            'type': 'linear',
            'target_snr': 2.0
        },
        'Other_type': {
            'type': 'linear',
            'target_snr': 2.0
        }
    }


def main():
    """主函数 - 使用示例"""
    print("=" * 60)
    print("简化的SCM数据生成和DAG提取工具")
    print("=" * 60)

    # 创建配置
    snr_config = SNRConfig(
        parent_snr_values=[2.0, 4.0],
        child_snr_multipliers=[0.5, 1.0],
        other_snr_multipliers=[0.5, 1.0]
    )

    node_function_configs = NodeFunctionConfigs(
        target_config=FunctionConfig(
            function_type='random_neural_network',
            hidden_dim=2,
            depth=3,
            activation='tanh',
            g_function_type='identity'
        ),
        target_child_config=FunctionConfig(
            function_type='linear',
            g_function_type='identity'
        ),
        other_config=FunctionConfig(
            function_type='linear',
            g_function_type='identity'
        )
    )

    test_config = TestConfig(
        n_datasets=2,
        custom_dag_type='random_SF',
        custom_dag_size='small',
        device='cuda' if torch.cuda.is_available() else 'cpu',
        snr_config=snr_config,
        node_function_configs=node_function_configs
    )

    # 初始化数据生成器
    generator = SimpleDataGenerator(device=test_config.device, seed=42, test_config=test_config)

    # 创建输出目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = f'simple_data_output_{timestamp}'
    os.makedirs(output_dir, exist_ok=True)

    print(f"输出目录: {output_dir}")
    print(f"使用设备: {test_config.device}")

    # 示例1: 生成带干预的数据集（使用配置类）
    print("\n" + "=" * 40)
    print("示例1: 生成带干预的数据集（使用配置类）")
    print("=" * 40)

    # 生成自定义函数配置
    custom_functions_configs = generator.generate_custom_functions_configs(
        snr_config, node_function_configs
    )

    # 使用第一个配置生成数据
    first_config_key = list(custom_functions_configs.keys())[0]
    first_config = custom_functions_configs[first_config_key]['config']

    datasets_with_intervention = generator.generate_scm_data(
        n_datasets=2,
        dag_type=test_config.custom_dag_type,
        dag_size=test_config.custom_dag_size,
        generate_intervention=True,
        custom_functions=first_config,
        avg_in_degree=test_config.avg_in_degree
    )

    # 保存数据集和DAG图
    for i, dataset in enumerate(datasets_with_intervention):
        print(f"\n处理数据集 {i+1}: {dataset['dataset_name']}")

        # 打印基本信息
        print(f"  训练集大小: {dataset['train_x'].shape}")
        print(f"  测试集大小: {dataset['test_x_original'].shape}")
        print(f"  干预测试集大小: {dataset['test_x_intervention'].shape}")
        print(f"  特征名称: {dataset['feature_names']}")
        print(f"  目标变量: {dataset['target_name']}")
        print(f"  DAG节点数: {dataset['dag_info']['num_nodes']}")
        print(f"  DAG边数: {dataset['dag_info']['num_edges']}")

        # 保存DAG可视化
        dag_path = os.path.join(output_dir, f'dag_intervention_{i+1}.png')
        generator.save_dag_visualization(dataset['scm'], dag_path)

        # 保存数据到CSV
        import pandas as pd

        # 保存训练集
        train_df = pd.DataFrame(dataset['train_x'], columns=dataset['feature_names'])
        train_df[dataset['target_name']] = dataset['train_y']
        train_df.to_csv(os.path.join(output_dir, f'train_data_{i+1}.csv'), index=False)

        # 保存原始测试集
        test_orig_df = pd.DataFrame(dataset['test_x_original'], columns=dataset['feature_names'])
        test_orig_df[dataset['target_name']] = dataset['test_y_original']
        test_orig_df.to_csv(os.path.join(output_dir, f'test_original_{i+1}.csv'), index=False)

        # 保存干预测试集
        test_intv_df = pd.DataFrame(dataset['test_x_intervention'], columns=dataset['feature_names'])
        test_intv_df[dataset['target_name']] = dataset['test_y_intervention']
        test_intv_df.to_csv(os.path.join(output_dir, f'test_intervention_{i+1}.csv'), index=False)

    # 示例2: 生成标准数据集（无干预）
    print("\n" + "=" * 40)
    print("示例2: 生成标准数据集（无干预）")
    print("=" * 40)

    datasets_standard = generator.generate_scm_data(
        n_datasets=1,
        dag_type='chain',
        dag_size='small',
        generate_intervention=False
    )

    # 保存标准数据集
    for i, dataset in enumerate(datasets_standard):
        print(f"\n处理标准数据集 {i+1}: {dataset['dataset_name']}")

        # 打印基本信息
        print(f"  训练集大小: {dataset['train_x'].shape}")
        print(f"  测试集大小: {dataset['test_x'].shape}")
        print(f"  特征名称: {dataset['feature_names']}")
        print(f"  目标变量: {dataset['target_name']}")

        # 保存DAG可视化
        dag_path = os.path.join(output_dir, f'dag_standard_{i+1}.png')
        generator.save_dag_visualization(dataset['scm'], dag_path)

        # 保存数据到CSV
        import pandas as pd

        # 保存训练集
        train_df = pd.DataFrame(dataset['train_x'], columns=dataset['feature_names'])
        train_df[dataset['target_name']] = dataset['train_y']
        train_df.to_csv(os.path.join(output_dir, f'standard_train_{i+1}.csv'), index=False)

        # 保存测试集
        test_df = pd.DataFrame(dataset['test_x'], columns=dataset['feature_names'])
        test_df[dataset['target_name']] = dataset['test_y']
        test_df.to_csv(os.path.join(output_dir, f'standard_test_{i+1}.csv'), index=False)

    # 生成摘要报告
    all_datasets = datasets_with_intervention + datasets_standard
    summary = generator.get_dataset_summary(all_datasets)

    print("\n" + "=" * 40)
    print("数据集摘要")
    print("=" * 40)
    for key, value in summary.items():
        print(f"  {key}: {value}")

    # 保存摘要到文件
    import json
    with open(os.path.join(output_dir, 'summary.json'), 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)

    print(f"\n所有文件已保存到: {output_dir}")
    print("生成完成！")


if __name__ == "__main__":
    main()
